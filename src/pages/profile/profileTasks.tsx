import { TaskStatusBadge, type TaskStatusType } from "@components/badge";
import { Avatar, Container } from "@components/common";
import { cn } from "@utils/cn";
import { getToday } from "@utils/date";

interface TaskProps {
  assignee: string;
  createdAt: string;
  detail: string;
  dueDate: string;
  source: string;
  status: TaskStatusType;
  title: string;
}

const tasks: TaskProps[] = [
  {
    assignee: "",
    createdAt: getToday(),
    detail:
      "แตงโม แตงโม แตงโม แตงโมลูกโตโตรสหวาน ใครรับประทาน ถูกอกถูกใจ แตงโมจินตหรามาใหม่ แตงโมจินตหรามาใหม่ ผ่าดูข้างใน เนื้อแดงจ่ายหว่าย วันนี้มาขายราคาไม่แพง วันนี้มาขายราคาไม่แพง",
    dueDate: getToday(),
    source: "https://www.google.com/?client=safari",
    status: "upcoming",
    title: "Task Title",
  },
  {
    assignee: "",
    createdAt: getToday(),
    detail:
      "แตงโม แตงโม แตงโม แตงโมลูกโตโตรสหวาน ใครรับประทาน ถูกอกถูกใจ แตงโมจินตหรามาใหม่ แตงโมจินตหรามาใหม่ ผ่าดูข้างใน เนื้อแดงจ่ายหว่าย วันนี้มาขายราคาไม่แพง วันนี้มาขายราคาไม่แพง",
    dueDate: getToday(),
    source: "https://www.google.com/?client=safari",
    status: "onboarding",
    title: "Task Title",
  },
  {
    assignee: "",
    createdAt: getToday(),
    detail:
      "แตงโม แตงโม แตงโม แตงโมโตโตรสหวาน ใครอกใจ แตงโมจินตหรามาใหม่ แตงโมจินตหรามาใหม่ ผ่าข้างใน เนื้อแดงจ่ายหว่ายมาขายราคาไม่แพงมาขายราคาไม่แพง",
    dueDate: getToday(),
    source: "https://www.google.com/?client=safari",
    status: "overdue",
    title: "Task Title",
  },
  {
    assignee: "",
    createdAt: getToday(),
    detail:
      "แตงโม แตงโม แตงโม แตงโมโตโตรสหวาน ใครอกใจ แตงโมจินตหรามาใหม่ แตงโมจินตหรามาใหม่ ผ่าข้างใน เนื้อแดงจ่ายหว่ายมาขายราคาไม่แพงมาขายราคาไม่แพง",
    dueDate: getToday(),
    source: "https://www.google.com/?client=safari",
    status: "completed",
    title: "Task Title",
  },
];

const CARD_STYLE = {
  completed: "text-neutral-content",
  onboarding: "border-primary-content bg-success-content shadow-lg",
  overdue: "text-neutral bg-base-200/50",
  upcoming: "text-info border-secondary-content bg-accent-content",
};

const TaskCard = (task: TaskProps) => {
  return (
    <Container className={cn("!h-fit gap-4 rounded-lg border p-2", CARD_STYLE[task.status])}>
      <div className="flex items-center gap-4">
        <h5>{task.title}</h5>
        <TaskStatusBadge type={task.status} label={task.status} className="rounded-lg" />
        <p className="text-body-sm">{task.dueDate}</p>
      </div>

      <div className="flow-root" />

      <h6>Detail:</h6>
      <p className="whitespace-pre-wrap text-body-sm">{task.detail}</p>

      <div className="flow-root" />

      <div className="">
        <h6>Source:</h6>
        <a href={task.source} className="text-blue-400 text-body-sm underline">
          {task.source}
        </a>
      </div>

      <div className="flow-root" />

      <div className="flex justify-between">
        <div className="flex items-center gap-2">
          <p className="text-label-xs">Assignee :</p>
          <Avatar image={task.assignee} />
        </div>
        <p className="text-label-xs">Create at : {task.createdAt}</p>
      </div>
    </Container>
  );
};

export const ProfileTasks = () => {
  return (
    <div className="m-4 flex-1 space-y-4 overflow-auto p-1">
      {tasks.map((task) => (
        <TaskCard key={task.title} {...task} />
      ))}
    </div>
  );
};
